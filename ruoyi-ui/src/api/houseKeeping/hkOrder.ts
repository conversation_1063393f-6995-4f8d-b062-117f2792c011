import type { R, TableDataInfo } from '@/api/model/resultModel';
import type { HkOrderForm, HkOrderQuery, HkOrderVo } from '@/api/houseKeeping/model/hkOrderModel';
import { request } from '@/utils/request';

/**
 * 查询支付订单列表
 * @param query 查询参数
 */
export function listHkOrder(query?: HkOrderQuery) {
  return request.get<TableDataInfo<HkOrderVo>>({
    url: '/houseKeeping/hkOrder/list',
    params: query,
  });
}

/**
 * 查询支付订单详细
 * @param orderId 主键
 */
export function getHkOrder(orderId: string) {
  return request.get<R<HkOrderVo>>({
    url: `/houseKeeping/hkOrder/${orderId}`,
  });
}

/**
 * 新增支付订单
 * @param data 表单数据
 */
export function addHkOrder(data: HkOrderForm) {
  return request.post<R<void>>({
    url: '/houseKeeping/hkOrder',
    data,
  });
}

/**
 * 修改支付订单
 * @param data
 */
export function updateHkOrder(data: HkOrderForm) {
  return request.put<R<void>>({
    url: '/houseKeeping/hkOrder',
    data,
  });
}

/**
 * 删除支付订单
 * @param orderIds 主键串
 */
export function delHkOrder(orderIds: string | Array<string>) {
  return request.delete<R<void>>({
    url: `/houseKeeping/hkOrder/${orderIds}`,
  });
}

/**
 * 发起支付
 * @param reservationId 预约ID
 */
export function payOrder(reservationId: string) {
  return request.post<R<any>>({
    url: `/houseKeeping/hkOrder/pay/${reservationId}`,
  });
}
