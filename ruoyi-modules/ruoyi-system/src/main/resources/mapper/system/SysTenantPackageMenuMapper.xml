<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.system.mapper.SysTenantPackageMenuMapper">

    <resultMap id="SysTenantPackageMenuResult" autoMapping="true" type="org.dromara.system.domain.SysTenantPackageMenu">
        <id property="packageId" column="package_id"/>
        <id property="menuId" column="menu_id"/>
    </resultMap>

    <sql id="selectSysTenantPackageMenu">
        select stpm.package_id, stpm.menu_id from sys_tenant_package_menu stpm
    </sql>
    <delete id="deleteTenantRoleMenuId">
        delete srm
        from sys_role_menu srm
                 join sys_role sr on srm.role_id = sr.role_id
                 join sys_tenant st on sr.tenant_id = st.tenant_id
        where st.package_id = #{packageId}
          and srm.menu_id in
        <foreach collection="menuIds" item="item" open="(" separator="," close=")">#{item}</foreach>
    </delete>
    <insert id="addTenantRoleAdminMenuId">
        insert into sys_role_menu
        select sr.role_id, stpm.menu_id
        from sys_role sr
                 join sys_tenant st on sr.tenant_id = st.tenant_id
                 join sys_tenant_package_menu stpm on st.package_id = stpm.package_id
        where st.package_id = #{packageId}
          and sr.role_key = 'admin'
          and stpm.menu_id in
        <foreach collection="menuIds" item="item" open="(" separator="," close=")">#{item}</foreach>
    </insert>

</mapper>
