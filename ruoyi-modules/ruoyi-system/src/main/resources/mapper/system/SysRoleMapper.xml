<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.system.mapper.SysRoleMapper">

    <resultMap id="SysRoleResult" autoMapping="true" type="org.dromara.system.domain.SysRole">
    </resultMap>
    <resultMap id="SysRoleResultVo" autoMapping="true" type="org.dromara.system.domain.vo.SysRoleVo">
    </resultMap>

    <sql id="selectRoleVo">
        select distinct r.role_id,
                        r.role_name,
                        r.role_key,
                        r.role_sort,
                        r.data_scope,
                        r.menu_check_strictly,
                        r.dept_check_strictly,
                        r.status,
                        r.del_flag,
                        r.create_time,
                        r.update_time,
                        r.remark
        from sys_role r
                 left join sys_user_role sur on sur.role_id = r.role_id
                 left join sys_user u on u.user_id = sur.user_id
                 left join sys_dept d on u.dept_id = d.dept_id
    </sql>
    <select id="queryList" parameterType="org.dromara.system.domain.query.SysRoleQuery" resultMap="SysRoleResultVo">
        <include refid="selectRoleVo"/>
        <where>
            and r.del_flag = '0'
            <if test="roleId != null"> and r.role_id = #{roleId}</if>
            <if test="roleName != null and roleName != ''"> and r.role_name like concat(concat('%', #{roleName}), '%')</if>
            <if test="roleKey != null and roleKey != ''"> and r.role_key like concat(concat('%', #{roleKey}), '%')</if>
            <if test="status != null and status != ''"> and r.status = #{status}</if>
            <if test="params.beginTime != null and params.endTime != null"> and r.create_time between #{params.beginTime} and #{params.endTime}</if>
            <foreach collection="roleIds" item="item" open=" and r.role_id in (" close=")" separator="," nullable="true">
                #{item}
            </foreach>
        </where>
        order by r.role_sort, r.create_time desc
    </select>

    <select id="selectPageRoleList" resultMap="SysRoleResult">
        <include refid="selectRoleVo"/>
        ${ew.getCustomSqlSegment}
    </select>

    <select id="selectRolePermissionByUserId" parameterType="Long" resultMap="SysRoleResultVo">
        <include refid="selectRoleVo"/>
        WHERE r.del_flag = '0' and sur.user_id = #{userId}
    </select>

    <select id="selectRolesByUserId" parameterType="Long" resultMap="SysRoleResultVo">
        select r.role_id,
               r.role_name,
               r.role_key,
               r.role_sort,
               r.data_scope,
               r.status
        from sys_role r
        WHERE r.del_flag = '0' and r.role_id in (select role_id from sys_user_role where user_id = #{userId})
    </select>

    <select id="selectRoleById" resultMap="SysRoleResultVo">
        <include refid="selectRoleVo"/>
        WHERE r.del_flag = '0' and r.role_id = #{roleId}
    </select>

</mapper>
