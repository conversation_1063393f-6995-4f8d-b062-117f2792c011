package org.dromara.houseKeeping.service;

import cn.hutool.json.JSONObject;
import jakarta.validation.constraints.NotNull;
import org.dromara.common.core.domain.R;
import org.dromara.houseKeeping.domain.HkOrder;
import org.dromara.houseKeeping.domain.bo.HkOrderBo;
import org.dromara.houseKeeping.domain.query.HkOrderQuery;
import org.dromara.houseKeeping.domain.vo.HkOrderVo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 支付订单Service接口
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
public interface IHkOrderService extends IService<HkOrder> {

    /**
     * 查询支付订单
     *
     * @param orderId 主键
     * @return HkOrderVo
     */
    HkOrderVo queryById(String orderId);

    /**
     * 分页查询支付订单列表
     *
     * @param query 查询对象
     * @return 支付订单分页列表
     */
    TableDataInfo<HkOrderVo> queryPageList(HkOrderQuery query);

    /**
     * 查询支付订单列表
     *
     * @param query 查询对象
     * @return 支付订单列表
     */
    List<HkOrderVo> queryList(HkOrderQuery query);

    /**
     * 新增支付订单
     *
     * @param bo 支付订单新增业务对象
     * @return 是否新增成功
     */
    Boolean insertByBo(HkOrderBo bo);

    /**
     * 修改支付订单
     *
     * @param bo 支付订单编辑业务对象
     * @return 是否修改成功
     */
    Boolean updateByBo(HkOrderBo bo);

    /**
     * 批量删除支付订单信息
     *
     * @param ids 待删除的主键集合
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<String> ids);

    R<JSONObject> pay(@NotNull(message = "预约编号不能为空") String reservationId);

    /**
     * 处理支付回调通知
     *
     * @param pid 商户ID
     * @param trade_no 支付平台订单号
     * @param out_trade_no 商户订单号
     * @param type 支付方式
     * @param name 商品名称
     * @param money 支付金额
     * @param trade_status 支付状态
     * @param sign 签名
     * @param sign_type 签名类型
     * @return 是否处理成功
     */
    Boolean handlePaymentNotify(String pid, String trade_no, String out_trade_no,
                               String type, String name, String money,
                               String trade_status, String sign, String sign_type);
}
