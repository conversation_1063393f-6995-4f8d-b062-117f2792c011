package org.dromara.houseKeeping.service;

import jakarta.validation.constraints.NotBlank;
import org.dromara.houseKeeping.domain.Reservation;
import org.dromara.houseKeeping.domain.bo.ReservationBo;
import org.dromara.houseKeeping.domain.query.ReservationQuery;
import org.dromara.houseKeeping.domain.vo.ReservationVo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 预约管理Service接口
 *
 * <AUTHOR>
 * @date 2025-07-27
 */
public interface IReservationService extends IService<Reservation> {

    /**
     * 查询预约管理
     *
     * @param reservationId 主键
     * @return ReservationVo
     */
    ReservationVo queryById(String reservationId);

    /**
     * 分页查询预约管理列表
     *
     * @param query 查询对象
     * @return 预约管理分页列表
     */
    TableDataInfo<ReservationVo> queryPageList(ReservationQuery query);

    /**
     * 查询预约管理列表
     *
     * @param query 查询对象
     * @return 预约管理列表
     */
    List<ReservationVo> queryList(ReservationQuery query);

    /**
     * 新增预约管理
     *
     * @param bo 预约管理新增业务对象
     * @return 是否新增成功
     */
    Boolean insertByBo(ReservationBo bo);

    /**
     * 修改预约管理
     *
     * @param bo 预约管理编辑业务对象
     * @return 是否修改成功
     */
    Boolean updateByBo(ReservationBo bo);

    /**
     * 批量删除预约管理信息
     *
     * @param ids 待删除的主键集合
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<String> ids);

    int cancelReservation(String reservationId, String cancelReason);
}
