package org.dromara.houseKeeping.Configuration;

import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Mapay支付配置类
 *
 * <AUTHOR>
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "mapay")
public class MapayConfiguration {

    /**
     * 商户号
     */
    private String pid;

    /**
     * 密钥
     */
    private String key;

    /**
     * 对接接口
     */
    private String baseUrl;

    /**
     * 回调地址
     */
    private String notifyUrl;

    /**
     * 支付方式
     */
    private String type;

    /**
     * 前端页面跳转地址
     */
    private String returnUrl;

    /**
     * 配置加载完成后的初始化方法
     */
    @PostConstruct
    public void init() {
        System.out.println("=== Mapay支付配置类加载完成 ===");
        System.out.println("商户号: " + maskSensitiveInfo(pid));
        System.out.println("密钥: " + maskSensitiveInfo(key));
        System.out.println("接口地址: " + baseUrl);
        System.out.println("回调地址: " + notifyUrl);
        System.out.println("前端跳转地址: " + returnUrl);
        System.out.println("支付方式: " + type);
        System.out.println("=== Mapay支付配置初始化成功 ===");
    }

    /**
     * 脱敏处理敏感信息
     * @param info 敏感信息
     * @return 脱敏后的信息
     */
    private String maskSensitiveInfo(String info) {
        if (info == null || info.length() <= 4) {
            return "****";
        }
        return info.substring(0, 2) + "****" + info.substring(info.length() - 2);
    }

}
