<template>
  <div class="test-container">
    <h2>支付功能测试</h2>
    
    <div class="test-section">
      <h3>测试支付接口</h3>
      <div class="test-form">
        <t-input 
          v-model="testReservationId" 
          placeholder="请输入预约ID"
          style="width: 300px; margin-right: 10px;"
        />
        <t-button 
          theme="primary" 
          @click="testPayment"
          :loading="loading"
        >
          测试支付
        </t-button>
      </div>
      
      <div v-if="result" class="test-result">
        <h4>测试结果：</h4>
        <pre>{{ JSON.stringify(result, null, 2) }}</pre>
      </div>
      
      <div v-if="error" class="test-error">
        <h4>错误信息：</h4>
        <pre>{{ error }}</pre>
      </div>
    </div>
    
    <div class="test-section">
      <h3>API 调用信息</h3>
      <div class="api-info">
        <p><strong>API 路径：</strong> /houseKeeping/reservation/pay</p>
        <p><strong>请求方法：</strong> GET</p>
        <p><strong>参数格式：</strong> { reservationId: "预约ID" }</p>
        <p><strong>预期响应：</strong> { code: 200, data: { payUrl: "支付链接" } }</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import { GetPayUrl } from '@/api/reservation.js'

const testReservationId = ref('1949514666408538113')
const loading = ref(false)
const result = ref(null)
const error = ref(null)

const testPayment = async () => {
  if (!testReservationId.value) {
    MessagePlugin.warning('请输入预约ID')
    return
  }
  
  loading.value = true
  result.value = null
  error.value = null
  
  try {
    console.log('测试支付，预约ID:', testReservationId.value)
    const response = await GetPayUrl(testReservationId.value)
    console.log('支付API响应:', response)
    
    result.value = response
    
    if (response.code === 200) {
      MessagePlugin.success('支付链接获取成功')
      if (response.data && response.data.payUrl) {
        console.log('支付链接:', response.data.payUrl)
      }
    } else {
      MessagePlugin.error(response.msg || '获取支付链接失败')
    }
  } catch (err) {
    console.error('支付测试失败:', err)
    error.value = err.message || err.toString()
    MessagePlugin.error('支付测试失败')
  } finally {
    loading.value = false
  }
}
</script>

<style lang="less" scoped>
.test-container {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 3rem;
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  
  h3 {
    margin-bottom: 1rem;
    color: #1f2937;
    font-size: 1.125rem;
    font-weight: 600;
  }
}

.test-form {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.test-result {
  margin-top: 1rem;
  padding: 1rem;
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 6px;
  
  h4 {
    margin-bottom: 0.5rem;
    color: #0369a1;
  }
  
  pre {
    background: white;
    padding: 1rem;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 0.875rem;
  }
}

.test-error {
  margin-top: 1rem;
  padding: 1rem;
  background: #fef2f2;
  border: 1px solid #f87171;
  border-radius: 6px;
  
  h4 {
    margin-bottom: 0.5rem;
    color: #dc2626;
  }
  
  pre {
    background: white;
    padding: 1rem;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 0.875rem;
    color: #dc2626;
  }
}

.api-info {
  background: #f9fafb;
  padding: 1rem;
  border-radius: 6px;
  
  p {
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    
    strong {
      color: #374151;
    }
  }
}

h2 {
  text-align: center;
  margin-bottom: 2rem;
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 700;
}
</style>
