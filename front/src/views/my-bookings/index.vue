<template>
  <div class="my-bookings-page">
    <!-- 全屏加载动画 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-container">
        <div class="loading-spinner">
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
        </div>
        <div class="loading-text">{{ loadingText }}</div>
        <div class="loading-dots">
          <span class="dot"></span>
          <span class="dot"></span>
          <span class="dot"></span>
        </div>
      </div>
    </div>

    <!-- Hero头部区域 -->
    <HeroSection
      v-model:search-query="searchQuery"
      @search="handleSearch"
    />

    <!-- 导航标签 -->
    <NavigationTabs
      :active-status="activeStatus"
      :status-tabs="statusTabs"
      @status-change="handleStatusChange"
    />

    <!-- 主要内容区域 -->
    <section class="content-section">
      <div class="container">
        <!-- 预约列表 -->
        <div class="bookings-section">
          <!-- 预约列表 -->
          <div v-if="bookingList.length > 0" class="bookings-list">
            <BookingCard
              v-for="booking in bookingList"
              :key="booking.reservationId"
              :booking="booking"
              @pay="handlePay"
              @cancel="handleCancel"
              @modify="handleModify"
              @rate="handleRate"
            />
          </div>
          <!-- 空状态 -->
          <div v-else-if="!loading" class="empty-state">
            <div class="empty-content">
              <div class="empty-illustration">
                <t-icon name="calendar" class="empty-icon" />
                <div class="empty-decoration"></div>
              </div>
              <h3 class="empty-title">暂无预约记录</h3>
              <p class="empty-description">
                {{ getEmptyDescription() }}
              </p>
              <div class="empty-actions">
                <t-button
                  theme="primary"
                  size="large"
                  @click="router.push('/services')"
                  class="empty-action-primary"
                >
                  <template #icon>
                    <t-icon name="add" />
                  </template>
                  立即预约服务
                </t-button>
                <t-button
                  theme="default"
                  size="large"
                  @click="router.push('/')"
                  class="empty-action-secondary"
                >
                  返回首页
                </t-button>
              </div>
            </div>
          </div>

          <!-- 分页组件 -->
          <div v-if="bookingList.length > 0" class="pagination-container">
            <t-pagination
              :current="currentPage"
              :total="totalBookings"
              :page-size="pageSize"
              :show-sizer="true"
              :show-jumper="true"
              :page-size-options="pageSizeOptions"
              @change="onPageChange"
            />
          </div>
        </div>
      </div>
    </section>

    <!-- 修改预约对话框 -->
    <t-dialog
      v-model:visible="modifyDialogVisible"
      header="修改预约信息"
      width="850px"
      :confirm-btn="null"
      :cancel-btn="null"
      @close="handleCloseModifyDialog"
    >
      <div class="modify-dialog-content">
        <t-form
          ref="modifyFormRef"
          :data="modifyForm"
          :rules="modifyFormRules"
          label-width="100px"
          @submit="handleSubmitModify"
        >
          <t-row :gutter="[24,24]">
            <t-col :span="6">
              <t-form-item label="联系人" name="contactName">
                <t-input
                  v-model="modifyForm.contactName"
                  placeholder="请输入联系人姓名"
                />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="联系电话" name="phone">
                <t-input
                  v-model="modifyForm.phone"
                  placeholder="请输入联系电话"
                  type="tel"
                />
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="服务地址" name="address">
                <t-textarea
                  v-model="modifyForm.address"
                  placeholder="请输入详细服务地址"
                  :autosize="{ minRows: 3, maxRows: 5 }"
                />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="服务日期" name="serviceDate">
                <t-date-picker
                  v-model="modifyForm.serviceDate"
                  placeholder="请选择日期"
                  clearable
                  :disable-date="disableDate"
                  style="width: 100%"
                />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="服务时段" name="serviceTime">
                <t-select
                  v-model="modifyForm.serviceTime"
                  :options="serviceTimeOptions"
                  placeholder="请选择预约时段"
                  style="width: 100%"
                />
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="特殊要求" name="special">
                <t-textarea
                  v-model="modifyForm.special"
                  placeholder="请输入特殊要求或注意事项（选填）"
                  :autosize="{ minRows: 3, maxRows: 5 }"
                />
              </t-form-item>
            </t-col>
          </t-row>
        </t-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <t-button theme="default" @click="handleCloseModifyDialog">
            取消
          </t-button>
          <t-button
            theme="primary"
            :loading="modifyLoading"
            @click="handleSubmitModify"
          >
            确认修改
          </t-button>
        </div>
      </template>
    </t-dialog>

    <!-- 取消预约对话框 -->
    <t-dialog
      v-model:visible="cancelDialogVisible"
      header="取消预约"
      width="650px"
      :confirm-btn="null"
      :cancel-btn="null"
      @close="handleCloseCancelDialog"
    >
      <div class="cancel-dialog-content">
        <div class="cancel-info">
          <p class="cancel-service-name">
            <strong>服务项目：</strong>{{ currentCancelBooking?.serve?.name }}
          </p>
          <p class="cancel-service-time">
            <strong>服务时间：</strong>{{ currentCancelBooking?.serviceDate }} {{ currentCancelBooking?.serviceTime }}
          </p>
        </div>

        <t-form
          ref="cancelFormRef"
          :data="cancelForm"
          :rules="cancelFormRules"
          label-width="80px"
          @submit="handleSubmitCancel"
        >
          <t-form-item label="取消原因" name="cancelReason">
            <t-textarea
              v-model="cancelForm.cancelReason"
              placeholder="请说明取消预约的原因（至少5个字符）"
              :maxlength="200"
              :autosize="{ minRows: 3, maxRows: 6 }"
              show-limit
            />
          </t-form-item>
        </t-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <t-button theme="default" @click="handleCloseCancelDialog">
            取消
          </t-button>
          <t-button
            theme="primary"
            :loading="cancelLoading"
            @click="handleSubmitCancel"
          >
            确认取消预约
          </t-button>
        </div>
      </template>
    </t-dialog>

  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next'
import { useRouter } from 'vue-router'
import { listReservation, updateReservation, cancelReservation } from "@/api/reservation.js"
import { GetPayUrl } from '@/api/hkOrder.js'
import { useUserStore } from '@/store/modules/user.js'

// 导入组件
import HeroSection from './HeroSection.vue'
import NavigationTabs from './NavigationTabs.vue'
import BookingCard from './BookingCard.vue'

const router = useRouter()
const userStore = useUserStore()


// 响应式数据
const loading = ref(false)
const loadingText = ref('正在加载预约数据...')
const activeStatus = ref('all')
const currentPage = ref(1)
const pageSize = ref(10)
const totalBookings = ref(0)
const bookingList = ref([])
const searchQuery = ref('')

// 修改预约相关数据
const modifyDialogVisible = ref(false)
const modifyLoading = ref(false)
const modifyFormRef = ref(null)
const currentModifyBooking = ref(null)

// 取消预约相关数据
const cancelDialogVisible = ref(false)
const cancelLoading = ref(false)
const cancelFormRef = ref(null)
const currentCancelBooking = ref(null)


// 修改预约表单数据
const modifyForm = reactive({
  contactName: '',
  phone: '',
  address: '',
  serviceDate: '',
  serviceTime: '',
  special: ''
})

// 取消预约表单数据
const cancelForm = reactive({
  cancelReason: ''
})



// 服务时间选项
const serviceTimeOptions = [
  { label: '08:00-10:00', value: '08:00-10:00' },
  { label: '10:00-12:00', value: '10:00-12:00' },
  { label: '14:00-16:00', value: '14:00-16:00' },
  { label: '16:00-18:00', value: '16:00-18:00' },
  { label: '19:00-21:00', value: '19:00-21:00' }
]

// 修改预约表单验证规则
const modifyFormRules = {
  contactName: [
    { required: true, message: '请输入联系人姓名', type: 'error' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', type: 'error' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', type: 'error' }
  ],
  address: [
    { required: true, message: '请输入服务地址', type: 'error' }
  ],
  serviceDate: [
    { required: true, message: '请选择服务日期', type: 'error' }
  ],
  serviceTime: [
    { required: true, message: '请选择服务时段', type: 'error' }
  ]
}

// 取消预约表单验证规则
const cancelFormRules = {
  cancelReason: [
    { required: true, message: '请输入取消原因', type: 'error' },
    { min: 5, message: '取消原因至少5个字符', type: 'error' },
    { max: 200, message: '取消原因不能超过200个字符', type: 'error' }
  ]
}

// 分页选项
const pageSizeOptions = ref([5, 10, 15, 20])

// 状态标签配置 - 与后端状态保持一致
const statusTabs = ref([
  { value: 'all', label: '全部' },
  { value: '待支付', label: '待支付' },
  { value: '已支付', label: '已支付' },
  { value: '已取消', label: '已取消' },
  { value: '待服务', label: '待服务' },
  { value: '服务中', label: '服务中' },
  { value: '已完成', label: '已完成' }
])

// 生命周期
onMounted(async () => {
  await getReservationList()
})

const getReservationList = async () => {
  try {
    loading.value = true
    loadingText.value = '正在加载预约数据...'

    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      customerId: userStore.userId,
      orderByColumn: "createTime",
      isAsc: "descending"
    }

    // 添加状态筛选
    if (activeStatus.value !== 'all') {
      params.status = activeStatus.value
    }

    // 添加搜索条件
    if (searchQuery.value.trim()) {
      params.serveName = searchQuery.value.trim()
    }

    const response = await listReservation(params)

    if (response.code !== 200) {
      await MessagePlugin.error(response.msg || "加载预约列表失败")
      return
    }

    // 处理返回数据
    bookingList.value = response.rows.map(item => ({
      ...item,
      // 解析服务标签
      serviceTags: item.serve?.tag ? JSON.parse(item.serve.tag) : []
    }))

    totalBookings.value = response.total || 0

  } catch (error) {
    await MessagePlugin.error(response.msg || '获取预约列表失败，请稍后重试' )
  } finally {
    loading.value = false
  }
}


// 事件处理方法
const handleStatusChange = async (status) => {
  activeStatus.value = status
  currentPage.value = 1 // 重置到第一页
  await getReservationList()
}

// 根据服务名称进行查询
const handleSearch = async () => {
  currentPage.value = 1 // 重置到第一页
  await getReservationList()
}

const onPageChange = async (pageInfo) => {
  currentPage.value = pageInfo.current
  pageSize.value = pageInfo.pageSize
  await getReservationList()
}

// 操作方法
const handleCancel = (booking) => {
  currentCancelBooking.value = booking
  // 重置表单
  cancelForm.cancelReason = ''
  cancelDialogVisible.value = true
}

const getPayUrl = async (booking) => {
  console.log("参数",booking);
  // const response = await GetPayUrl()
}


const handlePay = async (booking) => {
  try {
    console.log('开始支付，预约ID:', booking.reservationId);
    const response = await GetPayUrl(booking.reservationId)
    console.log("获取到的支付响应:", response);

    if(response.code !== 200){
      await MessagePlugin.error(response.msg || '获取支付链接失败，请稍后重试')
      return
    }

    // 检查支付链接是否存在
    if (response.data && response.data.payUrl) {
      console.log("打开支付链接:", response.data.payUrl);
      window.open(response.data.payUrl)
    } else {
      console.error("支付链接不存在:", response.data);
      await MessagePlugin.error('支付链接获取失败')
    }
  } catch (error) {
    console.error('支付请求失败:', error);
    await MessagePlugin.error('支付请求失败，请稍后重试')
  }
}

const handleModify = (booking) => {
  currentModifyBooking.value = booking

  // 填充表单数据
  modifyForm.contactName = booking.contactName
  modifyForm.phone = booking.phone
  modifyForm.address = booking.address
  modifyForm.serviceDate = booking.serviceDate
  modifyForm.serviceTime = booking.serviceTime
  modifyForm.special = booking.special || ''

  modifyDialogVisible.value = true
}

const handleRate = (booking) => {
  MessagePlugin.info('评价功能开发中...')
  // 这里可以跳转到评价页面或打开评价弹窗
}

// 关闭修改对话框
const handleCloseModifyDialog = () => {
  modifyDialogVisible.value = false
  currentModifyBooking.value = null

  // 使用表单的reset方法重置表单
  if (modifyFormRef.value) {
    modifyFormRef.value.reset()
  }
}

// 提交修改预约
const handleSubmitModify = async () => {
  if (!modifyFormRef.value) return

  // 表单验证
  const validateResult = await modifyFormRef.value.validate()
  if (validateResult !== true) {
    return
  }

  try {
    modifyLoading.value = true
    const updateData = {
      reservationId: currentModifyBooking.value.reservationId,
      ...modifyForm
    }
    const response = await updateReservation(updateData)
    if (response.code === 200) {
      handleCloseModifyDialog()
      // 重新加载预约列表
      await getReservationList()
      await MessagePlugin.success(response.msg || "修改成功")
    } else {
      await MessagePlugin.error(response.msg || '修改预约失败')
    }
  } catch (error) {
    await MessagePlugin.error('修改预约失败，请稍后重试')
  } finally {
    modifyLoading.value = false
  }
}

// 关闭取消对话框
const handleCloseCancelDialog = () => {
  cancelDialogVisible.value = false
  currentCancelBooking.value = null

  // 重置表单
  if (cancelFormRef.value) {
    cancelFormRef.value.reset()
  }
  cancelForm.cancelReason = ''
}

// 提交取消预约
const handleSubmitCancel = async () => {
  if (!cancelFormRef.value) return

  // 表单验证
  const validateResult = await cancelFormRef.value.validate()
  if (validateResult !== true) {
    return
  }
  try {
    cancelLoading.value = true
    // 调用取消预约的API
    const response = await cancelReservation({
      reservationId: currentCancelBooking.value.reservationId,
      cancelReason: cancelForm.cancelReason
    })
    if (response.code === 200) {
      handleCloseCancelDialog()
      await getReservationList() // 重新加载列表
      await MessagePlugin.success('预约已取消')
    } else {
      await MessagePlugin.error(response.msg || '取消预约失败')
    }
  } catch (error) {
    console.error('取消预约失败:', error)
    await MessagePlugin.error('取消预约失败，请稍后重试')
  } finally {
    cancelLoading.value = false
  }
}

// 禁用过去的日期
const disableDate = (date) => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return date < today
}



// 获取空状态描述
const getEmptyDescription = () => {
  const descriptions = {
    all: '您还没有任何预约记录，快去预约您需要的服务吧！',
    '待支付': '暂无待支付的预约',
    '已支付': '暂无已支付的预约',
    '已取消': '暂无已取消的预约',
    '待服务': '暂无待服务的预约',
    '服务中': '暂无服务中的预约',
    '已完成': '暂无已完成的预约'
  }
  return descriptions[activeStatus.value] || '暂无相关预约记录'
}


</script>

<style lang="less" scoped>
// 颜色变量
@primary: #165dff;
@secondary: #0e86d4;
@accent: #f59e0b;
@success: #00a870;
@warning: #ed7b2f;
@error: #d5494d;
@dark: #1f2937;
@light: #f9fafb;
@muted: #6b7280;
@border: #e5e7eb;

.my-bookings-page {
  min-height: 100vh;
  background: #f5f7fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

// 全屏加载动画
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(79, 70, 229, 0.1) 100%);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: overlayFadeIn 0.4s ease-out;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  animation: containerSlideIn 0.6s ease-out;
}

// 自定义旋转加载器
.loading-spinner {
  position: relative;
  width: 80px;
  height: 80px;
}

.spinner-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-radius: 50%;
  animation: spin 2s linear infinite;
}

.spinner-ring:nth-child(1) {
  border-top-color: #2563eb;
  animation-duration: 2s;
}

.spinner-ring:nth-child(2) {
  border-right-color: #3b82f6;
  animation-duration: 1.5s;
  animation-direction: reverse;
  width: 90%;
  height: 90%;
  top: 5%;
  left: 5%;
}

.spinner-ring:nth-child(3) {
  border-bottom-color: #60a5fa;
  animation-duration: 1s;
  width: 80%;
  height: 80%;
  top: 10%;
  left: 10%;
}

// 加载文字
.loading-text {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  text-align: center;
  letter-spacing: 0.5px;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

// 跳动的点
.loading-dots {
  display: flex;
  gap: 8px;
  align-items: center;
}

.dot {
  width: 8px;
  height: 8px;
  background: #2563eb;
  border-radius: 50%;
  animation: dotBounce 1.4s ease-in-out infinite both;
}

.dot:nth-child(1) {
  animation-delay: -0.32s;
}

.dot:nth-child(2) {
  animation-delay: -0.16s;
}

.dot:nth-child(3) {
  animation-delay: 0s;
}

// 动画定义
@keyframes overlayFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes containerSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes dotBounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

// 响应式设计
@media (max-width: 480px) {
  .loading-spinner {
    width: 60px;
    height: 60px;
  }

  .loading-text {
    font-size: 16px;
  }

  .dot {
    width: 6px;
    height: 6px;
  }
}

// BookingList组件样式
// 颜色变量
@border: #e5e7eb;
@muted: #6b7280;
@dark: #1f2937;

// 容器
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

// 内容区域
.content-section {
  padding: 40px 0;
}

// 预约列表
.bookings-section {
  // 移除背景和边框，让卡片独立显示
}

.bookings-list {
  display: flex;
  flex-direction: column;
  gap: 0; // 使用卡片自身的margin-bottom控制间距
}

// 空状态
.empty-state {
  padding: 80px 20px;
  text-align: center;
}

.empty-content {
  max-width: 500px;
  margin: 0 auto;
}

.empty-illustration {
  position: relative;
  margin-bottom: 32px;
}

.empty-icon {
  width: 80px;
  height: 80px;
  color: @muted;
  margin: 0 auto;
  display: block;
}

.empty-decoration {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120px;
  height: 120px;
  border: 2px dashed @border;
  border-radius: 50%;
  opacity: 0.5;
}

.empty-title {
  font-size: 24px;
  font-weight: 600;
  color: @dark;
  margin: 0 0 16px 0;
}

.empty-description {
  color: @muted;
  margin: 0 0 32px 0;
  line-height: 1.6;
  font-size: 16px;
}

.empty-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.empty-action-primary,
.empty-action-secondary {
  min-width: 140px;
}

// 修复按钮内图标和文字的垂直对齐
.empty-action-primary {
  // 使用template #icon后，TDesign会自动处理图标对齐
  // 这里只需要确保按钮本身的对齐
  :deep(.t-button) {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// 分页
.pagination-container {
  padding: 1.5rem;
  border-top: 1px solid @border;
  display: flex;
  justify-content: center;
}

// BookingList响应式设计
@media (max-width: 768px) {
  .content-section {
    padding: 30px 0;
  }

  .empty-state {
    padding: 60px 20px;
  }

  .empty-icon {
    width: 60px;
    height: 60px;
  }

  .empty-decoration {
    width: 90px;
    height: 90px;
  }

  .empty-title {
    font-size: 20px;
  }

  .empty-actions {
    flex-direction: column;
    align-items: center;
  }

  .empty-action-primary,
  .empty-action-secondary {
    width: 100%;
    max-width: 280px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 15px;
  }
}

// 修改预约对话框样式
.modify-dialog-content {
  padding: 1rem 0;
}

// 取消对话框样式
.cancel-dialog-content {
  padding: 1rem 0;
}

.cancel-info {
  background: @light;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.cancel-service-name,
.cancel-service-time {
  margin: 0 0 0.5rem 0;
  color: @dark;
  font-size: 0.875rem;

  &:last-child {
    margin-bottom: 0;
  }

  strong {
    color: @dark;
    font-weight: 600;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding-top: 1rem;
  border-top: 1px solid @border;
}

// 表单样式优化
:deep(.t-form-item) {
  margin-bottom: 1.5rem;
}

:deep(.t-form-item__label) {
  font-weight: 500;
  color: @dark;
}

:deep(.t-input),
:deep(.t-textarea),
:deep(.t-date-picker),
:deep(.t-select) {
  width: 100%;
}

:deep(.t-textarea .t-textarea__inner) {
  resize: vertical;
  min-height: 80px;
}

// 响应式对话框
@media (max-width: 768px) {
  :deep(.t-dialog) {
    width: 95% !important;
    margin: 1rem auto;
  }

  .modify-dialog-content {
    padding: 0.5rem 0;
  }

  :deep(.t-form-item) {
    margin-bottom: 1rem;
  }

  .dialog-footer {
    flex-direction: column-reverse;
    gap: 0.5rem;
  }

  .dialog-footer .t-button {
    width: 100%;
  }
}
</style>
